import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from "@/contexts/LanguageContext";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "María García - Guía Cultural",
  description: "Guía acompañante de rutas culturales especializada en experiencias auténticas por Europa, Marruecos y más destinos fascinantes.",
  keywords: "guía turística, tours culturales, viajes, España, Marruecos, Europa, turismo cultural",
  authors: [{ name: "<PERSON>" }],
  openGraph: {
    title: "María García - Guía Cultural",
    description: "Descubre el mundo a través de experiencias culturales auténticas",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <body className={`${inter.className} antialiased`}>
        <LanguageProvider>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-grow">
              {children}
            </main>
            <Footer />
          </div>
        </LanguageProvider>
      </body>
    </html>
  );
}
