import esTranslations from '@/content/i18n/es.json';
import enTranslations from '@/content/i18n/en.json';
import frTranslations from '@/content/i18n/fr.json';

export type Language = 'es' | 'en' | 'fr';

export const languages: Language[] = ['es', 'en', 'fr'];

export const languageNames = {
  es: 'Español',
  en: 'English',
  fr: 'Français'
};

export const languageFlags = {
  es: '🇪🇸',
  en: '🇺🇸',
  fr: '🇫🇷'
};

const translations = {
  es: esTranslations,
  en: enTranslations,
  fr: frTranslations
};

export function getTranslations(language: Language) {
  return translations[language] || translations.es;
}

export function getNestedValue(obj: any, path: string): string {
  return path.split('.').reduce((current, key) => current?.[key], obj) || path;
}

export function t(key: string, language: Language): string {
  const translations = getTranslations(language);
  return getNestedValue(translations, key);
}
